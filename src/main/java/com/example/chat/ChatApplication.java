package com.example.chat;

import com.example.chat.model.OpenAIRequest;
import com.example.chat.service.DefaultFunctionCallManager;
import com.example.chat.service.OpenaiClient;

import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 聊天应用程序主启动类
 * 
 * 这个类负责：
 * 1. 创建 CopilotChatAssistant 实例
 * 2. 注册各种事件监听器
 * 3. 设置初始消息数据
 * 4. 启动应用程序
 */
public class ChatApplication {
    
    private CopilotChatAssistant chatAssistant;
    private List<OpenAIRequest.Message> messageHistory;
    private OpenaiClient openaiClient;
    private DefaultFunctionCallManager setFunctionCallManager;

    public ChatApplication() {
        this.messageHistory = new ArrayList<>();
        this.openaiClient = new OpenaiClient();
        initializeApplication();
    }
    
    /**
     * 初始化应用程序
     */
    private void initializeApplication() {
        // 设置系统外观
        setupLookAndFeel();
        
        // 创建聊天助手实例
        chatAssistant = new CopilotChatAssistant();
        
        // 注册各种监听器
        registerEventListeners();
        
        // 设置初始消息
//        setupInitialMessages();

        // 注册functionCall
        registerFunctionCalls();

        // 显示界面
        chatAssistant.setVisible(true);
        
        System.out.println("=== 聊天应用程序已启动 ===");
        System.out.println("功能说明：");
        System.out.println("1. 在输入框中输入消息并按回车或点击发送按钮");
        System.out.println("2. 按Ctrl+回车可以在输入框中换行");
        System.out.println("3. 点击标题栏的垃圾桶图标可以清理整个会话");
        System.out.println("4. 鼠标悬停到消息上可以看到复制和删除按钮");
        System.out.println("5. 右键点击消息可以选择删除从该消息开始的所有消息");
        System.out.println("6. 查看控制台输出了解各种事件");
    }

    private void registerFunctionCalls() {
        this.setFunctionCallManager = new com.example.chat.service.DefaultFunctionCallManager();
        this.setFunctionCallManager.registerTool(com.example.chat.tools.DirectoryListTool.createTool());
        openaiClient.setFunctionCallManager(setFunctionCallManager);
    }

    /**
     * 设置系统外观
     */
    private void setupLookAndFeel() {
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            System.err.println("无法设置系统外观: " + e.getMessage());
        }
        
        // 设置抗锯齿
        System.setProperty("awt.useSystemAAFontSettings", "on");
        System.setProperty("swing.aatext", "true");
    }
    
    /**
     * 注册各种事件监听器
     */
    private void registerEventListeners() {
        // 注册消息发送监听器
        chatAssistant.onMessageSent(this::handleUserMessage);
        
        // 注册清理会话监听器
        chatAssistant.onClearSession(this::handleClearSession);
        
        // 注册删除消息监听器
        chatAssistant.onDeleteMessages(this::handleDeleteMessages);

        // 注册编辑消息监听器
        chatAssistant.onMessageEdited(this::handleEditMessages);
    }


    /**
     * 处理用户发送的消息
     */
    private void handleUserMessage(String userMessage) {
        System.out.println("用户发送消息: " + userMessage);
        
        // 添加用户消息到历史记录
        messageHistory.add(new ChatInterface.OpenAIMessage("user", userMessage));

        chatAssistant.setMessages(messageHistory);
        
        // 设置AI正在回复状态
        chatAssistant.setReplyingStatus(true);
        
        // 模拟AI处理和回复（实际应用中这里会调用AI API）
        simulateAIResponse(null);
    }
    
    /**
     * 调用真实的 OpenAI API 获取回复
     */
    private void simulateAIResponse(String userMessage) {
        // 在后台线程中调用 OpenAI API，避免阻塞 UI
        new Thread(() -> {
            try {
                System.out.println("正在调用 OpenAI API...");

                // 调用 OpenAI API，获取完整的消息列表
                List<OpenAIRequest.Message> updatedMessages = openaiClient.generateOpenaiResponse(messageHistory, userMessage);

                // 在 EDT 线程中更新 UI
                SwingUtilities.invokeLater(() -> {
                    // 更新消息历史
                    messageHistory.clear();
                    messageHistory.addAll(updatedMessages);

                    // 更新UI显示所有消息
                    chatAssistant.setMessages(new ArrayList<>(messageHistory));

                    // 设置AI回复完成状态
                    chatAssistant.setReplyingStatus(false);

                    // 打印最新的AI回复
                    for (int i = updatedMessages.size() - 1; i >= 0; i--) {
                        OpenAIRequest.Message msg = updatedMessages.get(i);
                        if ("assistant".equals(msg.getRole())) {
                            System.out.println("AI回复: " + msg.getContent());
                            break;
                        }
                    }
                });

            } catch (Exception e) {
                System.err.println("调用 OpenAI API 时发生错误: " + e.getMessage());
                e.printStackTrace();

                // 在 EDT 线程中显示错误信息
                SwingUtilities.invokeLater(() -> {
                    String errorMessage = "抱歉，我现在无法回复。请检查网络连接和 API Key 设置。\n错误信息：" + e.getMessage();
                    messageHistory.add(new OpenAIRequest.Message("user", userMessage));
                    messageHistory.add(new OpenAIRequest.Message("assistant", errorMessage));
                    chatAssistant.setMessages(new ArrayList<>(messageHistory));
                    chatAssistant.setReplyingStatus(false);
                });
            }
        }).start();
    }
    
    /**
     * 生成模拟的AI回复
     */
    private String generateOpenaiResponse(String userMessage) {
        String lowerMessage = userMessage.toLowerCase();
        
        if (lowerMessage.contains("hello") || lowerMessage.contains("hi") || lowerMessage.contains("你好")) {
            return "你好！我是GitHub Copilot，很高兴为你提供帮助。有什么我可以协助你的吗？";
        } else if (lowerMessage.contains("weather") || lowerMessage.contains("天气")) {
            return "我无法获取实时天气信息，但我建议你查看当地的天气预报应用或网站来获取准确的天气信息。";
        } else if (lowerMessage.contains("code") || lowerMessage.contains("编程") || lowerMessage.contains("代码")) {
            return "我很乐意帮助你解决编程问题！请告诉我你遇到的具体问题，比如使用的编程语言、错误信息或者你想要实现的功能。";
        } else if (lowerMessage.contains("help") || lowerMessage.contains("帮助")) {
            return "我可以帮助你解决各种问题，包括：\n• 编程和代码相关问题\n• 技术概念解释\n• 算法和数据结构\n• 软件开发最佳实践\n\n请告诉我你需要什么帮助！";
        } else {
            return "我收到了你的消息：\"" + userMessage + "\"\n\n" +
                   "这是一个模拟回复。在实际应用中，这里会调用真实的AI API来生成智能回复。\n\n" +
                   "你可以尝试发送包含以下关键词的消息来看到不同的回复：\n" +
                   "• hello, hi, 你好\n" +
                   "• weather, 天气\n" +
                   "• code, 编程, 代码\n" +
                   "• help, 帮助";
        }
    }
    
    /**
     * 处理清理会话请求
     */
    private void handleClearSession() {
        System.out.println("用户请求清理会话");
        
        // 清空消息历史
        messageHistory.clear();

        chatAssistant.setMessages(messageHistory);
        
//        // 重新设置初始消息
//        setupInitialMessages();
        
        System.out.println("会话已清理，重置为初始状态");
    }
    
    /**
     * 处理删除消息请求
     */
    private void handleDeleteMessages(List<Integer> messageIndices) {
        System.out.println("用户请求删除消息，索引: " + messageIndices);
        
        if (messageIndices.isEmpty()) {
            return;
        }
        
        // 找到最小的索引，删除从该索引开始的所有消息
        int minIndex = messageIndices.stream().mapToInt(Integer::intValue).min().orElse(0);
        
        // 从消息历史中删除指定索引及之后的所有消息
        if (minIndex < messageHistory.size()) {
            messageHistory = new ArrayList<>(messageHistory.subList(0, minIndex));
            
            // 更新UI
            chatAssistant.setMessages(new ArrayList<>(messageHistory));
            
            System.out.println("已删除索引 " + minIndex + " 及之后的所有消息");
        }
    }

    private void handleEditMessages(int i, String s) {
        messageHistory.get(i).setContent(s);
        chatAssistant.setMessages(messageHistory);
    }
    
    /**
     * 设置初始消息（硬编码的示例数据）
     */
    private void setupInitialMessages() {
        messageHistory.clear();
        
        // 系统消息
        messageHistory.add(new OpenAIRequest.Message("system",
            "You are GitHub Copilot, a helpful AI assistant for developers."));

        // 欢迎消息
        messageHistory.add(new OpenAIRequest.Message("assistant",
            "你好！我是GitHub Copilot，你的AI编程助手。\n\n" +
            "我可以帮助你：\n" +
            "• 解答编程问题\n" +
            "• 代码审查和优化建议\n" +
            "• 解释技术概念\n" +
            "• 调试和故障排除\n\n" +
            "有什么我可以帮助你的吗？"));

        // 示例对话 - 使用简单的对话，避免复杂的工具调用
        messageHistory.add(new OpenAIRequest.Message("user",
            "你能帮我解释一下什么是设计模式吗？"));

        // 直接的助手回答，不包含工具调用
        messageHistory.add(new OpenAIRequest.Message("assistant",
            "设计模式是软件开发中的重要概念！\n\n" +
            "**什么是设计模式？**\n" +
            "设计模式是针对软件设计中常见问题的可重用解决方案。它们是经过验证的最佳实践，可以帮助开发者写出更清晰、更灵活、更易维护的代码。\n\n" +
            "**主要分类：**\n" +
            "• **创建型模式**：如单例模式、工厂模式\n" +
            "• **结构型模式**：如适配器模式、装饰器模式\n" +
            "• **行为型模式**：如观察者模式、策略模式\n\n" +
            "**为什么要使用设计模式？**\n" +
            "1. 提高代码复用性\n" +
            "2. 增强代码可读性\n" +
            "3. 降低系统耦合度\n" +
            "4. 便于维护和扩展\n\n" +
            "你想了解哪种具体的设计模式呢？"));
        
        // 更新UI显示初始消息
        chatAssistant.setMessages(new ArrayList<>(messageHistory));
    }
    
    /**
     * 应用程序入口点
     */
    public static void main(String[] args) {
        // 检查是否启用debug模式
        boolean debugMode = Arrays.asList(args).contains("--debug") || Arrays.asList(args).contains("-d");
        
        if (debugMode) {
            System.out.println("Debug模式已启用");
        }
        
        // 在EDT线程中启动应用程序
        SwingUtilities.invokeLater(() -> {
            try {
                new ChatApplication();
            } catch (Exception e) {
                System.err.println("启动应用程序时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
