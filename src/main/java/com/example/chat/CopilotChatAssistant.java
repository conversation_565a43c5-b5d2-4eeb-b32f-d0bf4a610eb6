package com.example.chat;

import com.example.chat.model.OpenAIRequest;
import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.plaf.basic.BasicScrollBarUI;
import java.awt.*;
import java.awt.event.*;
import java.awt.geom.RoundRectangle2D;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class CopilotChatAssistant extends JFrame implements ChatInterface {
    
    // VS Code 深色主题配色
    private static final Color BACKGROUND = new Color(30, 30, 30);
    private static final Color SIDEBAR_BG = new Color(37, 37, 38);
    private static final Color INPUT_BG = new Color(60, 60, 60);
    private static final Color BORDER_COLOR = new Color(69, 69, 69);
    private static final Color TEXT_PRIMARY = new Color(204, 204, 204);
    private static final Color TEXT_SECONDARY = new Color(140, 140, 140);
    private static final Color USER_BUBBLE = new Color(0, 122, 255);
    private static final Color ASSISTANT_BUBBLE = new Color(45, 45, 45);
    private static final Color FUNCTION_CALL_BUBBLE = new Color(88, 129, 87);    // 绿色：函数调用
    private static final Color FUNCTION_RESULT_BUBBLE = new Color(102, 102, 102); // 灰色：函数结果
    private static final Color TOOL_BUBBLE = new Color(156, 100, 176);           // 紫色：工具消息
    private static final Color HOVER_COLOR = new Color(50, 50, 50);
    
    // 字体
    private static final Font MAIN_FONT = new Font("Segoe UI", Font.PLAIN, 13);
    private static final Font SMALL_FONT = new Font("Segoe UI", Font.PLAIN, 11);
    private static final Font TITLE_FONT = new Font("Segoe UI", Font.BOLD, 14);
    
    private JPanel chatContainer;
    private JScrollPane scrollPane;
    private JTextArea inputArea;
    private JButton sendButton;
    private List<Message> messages;
    private boolean isTyping = false;
    private MessageSentListener messageSentListener;
    private ClearSessionListener clearSessionListener;
    private DeleteMessagesListener deleteMessagesListener;
    private MessageEditedListener messageEditedListener;
    private boolean debugMode = false;  // Debug模式标志

    public CopilotChatAssistant() {
        this(false);  // 默认不启用debug模式
    }

    public CopilotChatAssistant(boolean debugMode) {
        this.debugMode = debugMode;
        this.messages = new ArrayList<>();
        initializeUI();
    }
    
    private void initializeUI() {
        setTitle("GitHub Copilot Chat");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(400, 700);
        setLocationRelativeTo(null);
        setBackground(BACKGROUND);
        
        // 主容器
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(SIDEBAR_BG);
        
        // 顶部标题栏
        JPanel headerPanel = createHeaderPanel();
        mainPanel.add(headerPanel, BorderLayout.NORTH);
        
        // 聊天区域
        createChatArea();
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        
        // 底部输入区域
        JPanel inputPanel = createInputPanel();
        mainPanel.add(inputPanel, BorderLayout.SOUTH);
        
        setContentPane(mainPanel);
        
        // 焦点到输入框
        SwingUtilities.invokeLater(() -> inputArea.requestFocus());

        // 如果启用debug模式，应用debug边框
        if (debugMode) {
            applyDebugBorders();
        }
    }

    /**
     * 为所有UI组件添加debug边框
     */
    private void applyDebugBorders() {
        // 为主要容器添加边框
        addDebugBorder(getContentPane(), "MainPanel", Color.RED);
        addDebugBorder(chatContainer, "ChatContainer", Color.BLUE);
        addDebugBorder(scrollPane, "ScrollPane", Color.GREEN);
        addDebugBorder(inputArea, "InputArea", Color.ORANGE);
        addDebugBorder(sendButton, "SendButton", Color.MAGENTA);
    }

    /**
     * 为单个组件添加debug边框
     */
    private void addDebugBorder(Component component, String name, Color color) {
        if (component instanceof JComponent) {
            JComponent jComponent = (JComponent) component;
            Border originalBorder = jComponent.getBorder();
            Border debugBorder = BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(color, 2),
                name,
                TitledBorder.LEFT,
                TitledBorder.TOP,
                new Font("Arial", Font.PLAIN, 10),
                color
            );

            if (originalBorder != null) {
                jComponent.setBorder(BorderFactory.createCompoundBorder(debugBorder, originalBorder));
            } else {
                jComponent.setBorder(debugBorder);
            }
        }
    }
    
    private JPanel createHeaderPanel() {
        JPanel header = new JPanel(new BorderLayout());
        header.setBackground(SIDEBAR_BG);
        header.setBorder(new EmptyBorder(12, 16, 12, 16));
        
        // 左侧标题
        JLabel titleLabel = new JLabel("GitHub Copilot");
        titleLabel.setFont(TITLE_FONT);
        titleLabel.setForeground(TEXT_PRIMARY);
        
        // 右侧按钮
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
        buttonPanel.setBackground(SIDEBAR_BG);
        
        JButton clearButton = createIconButton("🗑", "Clear chat");
        clearButton.addActionListener(e -> clearChat());
        
        JButton settingsButton = createIconButton("⚙", "Settings");
        
        buttonPanel.add(clearButton);
        buttonPanel.add(settingsButton);
        
        header.add(titleLabel, BorderLayout.WEST);
        header.add(buttonPanel, BorderLayout.EAST);

        // 如果启用debug模式，为header组件添加边框
        if (debugMode) {
            addDebugBorder(header, "HeaderPanel", Color.CYAN);
            addDebugBorder(titleLabel, "TitleLabel", Color.YELLOW);
            addDebugBorder(buttonPanel, "ButtonPanel", Color.PINK);
        }

        return header;
    }
    
    private JButton createIconButton(String icon, String tooltip) {
        JButton button = new JButton(icon);
        button.setFont(new Font("Segoe UI Symbol", Font.PLAIN, 14));
        button.setForeground(TEXT_SECONDARY);
        button.setBackground(SIDEBAR_BG);
        button.setBorder(new EmptyBorder(6, 8, 6, 8));
        button.setFocusPainted(false);
        button.setContentAreaFilled(false);
        button.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        button.setToolTipText(tooltip);
        
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(HOVER_COLOR);
                button.setContentAreaFilled(true);
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                button.setContentAreaFilled(false);
            }
        });
        
        return button;
    }
    
    private void createChatArea() {
        chatContainer = new JPanel();
        chatContainer.setLayout(new BoxLayout(chatContainer, BoxLayout.Y_AXIS));
        chatContainer.setBackground(BACKGROUND);
        chatContainer.setBorder(new EmptyBorder(16, 16, 16, 16));
        
        scrollPane = new JScrollPane(chatContainer);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setBorder(null);
        scrollPane.getViewport().setBackground(BACKGROUND);
        
        // 自定义滚动条
        customizeScrollBar();
    }
    
    private void customizeScrollBar() {
        JScrollBar verticalBar = scrollPane.getVerticalScrollBar();
        verticalBar.setUI(new BasicScrollBarUI() {
            @Override
            protected void configureScrollBarColors() {
                this.thumbColor = new Color(90, 90, 90);
                this.trackColor = BACKGROUND;
            }
            
            @Override
            protected JButton createDecreaseButton(int orientation) {
                return createInvisibleButton();
            }
            
            @Override
            protected JButton createIncreaseButton(int orientation) {
                return createInvisibleButton();
            }
            
            private JButton createInvisibleButton() {
                JButton button = new JButton();
                button.setPreferredSize(new Dimension(0, 0));
                return button;
            }
            
            @Override
            protected void paintThumb(Graphics g, JComponent c, Rectangle thumbBounds) {
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2.setColor(thumbColor);
                g2.fillRoundRect(thumbBounds.x + 2, thumbBounds.y,
                    thumbBounds.width - 4, thumbBounds.height, 6, 6);
                g2.dispose();
            }
        });
        verticalBar.setPreferredSize(new Dimension(8, 0));
    }
    
    private JPanel createInputPanel() {
        JPanel inputPanel = new JPanel(new BorderLayout());
        inputPanel.setBackground(SIDEBAR_BG);
        inputPanel.setBorder(new EmptyBorder(16, 16, 16, 16));
        
        // 输入区域容器
        JPanel inputContainer = new JPanel(new BorderLayout());
        inputContainer.setBackground(INPUT_BG);
        inputContainer.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(BORDER_COLOR, 1),
            new EmptyBorder(8, 12, 8, 8)
        ));
        
        // 多行输入框
        inputArea = new JTextArea();
        inputArea.setFont(MAIN_FONT);
        inputArea.setBackground(INPUT_BG);
        inputArea.setForeground(TEXT_PRIMARY);
        inputArea.setCaretColor(TEXT_PRIMARY);
        inputArea.setBorder(null);
        inputArea.setLineWrap(true);
        inputArea.setWrapStyleWord(true);
        inputArea.setMargin(new Insets(4, 0, 4, 0));
        inputArea.setRows(1);  // 默认1行
        inputArea.setColumns(30);  // 设置列数以确定宽度
        
        // 占位符文本
        String placeholder = "Ask GitHub Copilot or type \"/\" for commands...";
        inputArea.setText(placeholder);
        inputArea.setForeground(TEXT_SECONDARY);
        
        inputArea.addFocusListener(new FocusAdapter() {
            @Override
            public void focusGained(FocusEvent e) {
                if (inputArea.getText().equals(placeholder)) {
                    inputArea.setText("");
                    inputArea.setForeground(TEXT_PRIMARY);
                }
            }
            
            @Override
            public void focusLost(FocusEvent e) {
                if (inputArea.getText().trim().isEmpty()) {
                    inputArea.setText(placeholder);
                    inputArea.setForeground(TEXT_SECONDARY);
                }
            }
        });
        
        // Enter 发送消息，Ctrl+Enter 换行
        inputArea.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    if (e.isControlDown()) {
                        // Ctrl+Enter 换行
                        e.consume();
                        inputArea.append("\n");
                    } else {
                        // 单独的 Enter 发送消息
                        e.consume();
                        sendMessage();
                    }
                }
            }
        });
        
        // 发送按钮
        sendButton = new JButton("⮕");
        sendButton.setFont(new Font("Segoe UI Symbol", Font.BOLD, 16));
        sendButton.setForeground(TEXT_PRIMARY);
        sendButton.setBackground(USER_BUBBLE);
        sendButton.setBorder(new EmptyBorder(8, 12, 8, 12));
        sendButton.setFocusPainted(false);
        sendButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        sendButton.setToolTipText("Send message (Enter)");
        sendButton.addActionListener(e -> sendMessage());
        
        // 布局
        JScrollPane inputScroll = new JScrollPane(inputArea);
        inputScroll.setBorder(null);
        inputScroll.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        inputScroll.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        inputScroll.setBackground(INPUT_BG);
        inputScroll.getViewport().setBackground(INPUT_BG);
        inputScroll.setPreferredSize(new Dimension(0, 40));  // 设置首选高度，约1行文字
        inputScroll.setMaximumSize(new Dimension(Integer.MAX_VALUE, 120));  // 最大高度约3行
        
        inputContainer.add(inputScroll, BorderLayout.CENTER);
        inputContainer.add(sendButton, BorderLayout.EAST);
        
        // 底部提示
        JLabel hintLabel = new JLabel("Use Ctrl+Enter to send");
        hintLabel.setFont(SMALL_FONT);
        hintLabel.setForeground(TEXT_SECONDARY);
        hintLabel.setBorder(new EmptyBorder(8, 0, 0, 0));
        
        inputPanel.add(inputContainer, BorderLayout.CENTER);
        inputPanel.add(hintLabel, BorderLayout.SOUTH);

        // 如果启用debug模式，为input组件添加边框
        if (debugMode) {
            addDebugBorder(inputPanel, "InputPanel", Color.LIGHT_GRAY);
            addDebugBorder(inputContainer, "InputContainer", Color.GRAY);
            addDebugBorder(inputScroll, "InputScroll", Color.DARK_GRAY);
            addDebugBorder(hintLabel, "HintLabel", Color.BLACK);
        }

        return inputPanel;
    }
    
    private void sendMessage() {
        String text = inputArea.getText().trim();
        String placeholder = "Ask GitHub Copilot or type \"/\" for commands...";
        
        if (text.isEmpty() || text.equals(placeholder) || isTyping) {
            return;
        }
        
        // 清空输入框
        inputArea.setText("");
        inputArea.setForeground(TEXT_PRIMARY);
        
        // 触发回调，让外部处理消息
        if (messageSentListener != null) {
            messageSentListener.onMessageSent(text);
        }
    }
    
    private void startTyping() {
        isTyping = true;
        sendButton.setEnabled(false);
        
        // 添加输入提示
        JPanel typingPanel = createTypingIndicator();
        chatContainer.add(typingPanel);
        chatContainer.revalidate();
        scrollToBottom();
    }
    
    private void stopTyping() {
        isTyping = false;
        sendButton.setEnabled(true);
        
        // 移除输入提示
        Component[] components = chatContainer.getComponents();
        for (Component comp : components) {
            if (comp instanceof JPanel && "typing".equals(((JPanel) comp).getName())) {
                chatContainer.remove(comp);
                break;
            }
        }
        chatContainer.revalidate();
    }
    
    private JPanel createTypingIndicator() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBackground(BACKGROUND);
        panel.setName("typing");
        panel.setBorder(new EmptyBorder(8, 40, 8, 0));
        
        JLabel label = new JLabel("GitHub Copilot is typing...");
        label.setFont(SMALL_FONT);
        label.setForeground(TEXT_SECONDARY);
        
        panel.add(label);
        return panel;
    }
    
    /**
     * 格式化函数调用信息
     */
    private String formatFunctionCall(ChatInterface.ToolCall toolCall) {
        StringBuilder sb = new StringBuilder();
        sb.append("🔧 Calling function: ").append(toolCall.getFunction().getName()).append("\n\n");
        
        String args = toolCall.getFunction().getArguments();
        if (args != null && !args.trim().isEmpty()) {
            // 简单格式化 JSON
            sb.append("Parameters:\n").append(formatJsonForDisplay(args));
        }
        
        return sb.toString();
    }
    
    /**
     * 简单格式化 JSON 以便显示
     */
    private String formatJsonForDisplay(String json) {
        if (json == null || json.trim().isEmpty()) {
            return "{}";
        }
        
        try {
            // 简单的 JSON 美化（不依赖第三方库）
            return json.replace(",", ",\n  ")
                      .replace("{", "{\n  ")
                      .replace("}", "\n}");
        } catch (Exception e) {
            return json;
        }
    }
    
    /**
     * 根据消息类型获取气泡颜色
     */
    private Color getMessageBubbleColor(Message message) {
        if (message.isUser) {
            return USER_BUBBLE;
        }
        
        switch (message.role) {
            case "assistant":
                return ASSISTANT_BUBBLE;
            case "function":
                return FUNCTION_RESULT_BUBBLE;
            case "tool":
                return FUNCTION_RESULT_BUBBLE;
            case "tool_call":
                return FUNCTION_CALL_BUBBLE;
            case "system":
                return TOOL_BUBBLE;
            default:
                return ASSISTANT_BUBBLE;
        }
    }
    
    private void addMessage(Message message) {
        messages.add(message);
        
        JPanel messagePanel = createMessagePanel(message);
        chatContainer.add(messagePanel);
        chatContainer.add(Box.createVerticalStrut(12));
        
        scrollToBottom();
        chatContainer.revalidate();
        chatContainer.repaint();
    }

    private JPanel createMessagePanel(Message message) {
        // 调试信息
        System.out.println("创建消息面板: isUser=" + message.isUser + ", content=" + message.content + ", role=" + message.role);

        // 1. 最外层容器，使用水平BoxLayout，它将负责左右对齐
        JPanel container = new JPanel();
        container.setLayout(new BoxLayout(container, BoxLayout.X_AXIS));
        container.setBackground(BACKGROUND);
        container.setOpaque(false);

        // 获取消息气泡颜色
        Color bubbleColor = getMessageBubbleColor(message);

        // 2. 消息气泡 (这部分逻辑不变)
        JPanel bubble = new RoundedPanel(12, bubbleColor);
        bubble.setLayout(new BorderLayout());
        EmptyBorder bubbleBorder = new EmptyBorder(8, 16, 0, 16);
        bubble.setBorder(bubbleBorder);

        // 主内容面板
        JPanel contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));
        contentPanel.setOpaque(false);

        // 这里为了简洁，省略了您原有的详细填充逻辑，您只需保留即可
        if (message.hasToolCalls && !message.toolCalls.isEmpty()) {
            // (您原来的工具调用显示逻辑)
            // 1. 首先显示初始内容（如果有）
            if (message.initialContent != null && !message.initialContent.trim().isEmpty()) {
                JTextArea initialArea = new JTextArea(message.initialContent);
                initialArea.setFont(MAIN_FONT);
                initialArea.setForeground(TEXT_PRIMARY);
                initialArea.setBackground(bubbleColor);
                initialArea.setLineWrap(true);
                initialArea.setWrapStyleWord(true);
                initialArea.setEditable(false);
                initialArea.setBorder(null);
                initialArea.setOpaque(false);
                contentPanel.add(initialArea);
                contentPanel.add(Box.createVerticalStrut(8));
            }

            // 2. 然后显示工具调用过程
            JPanel toolSummaryPanel = createToolSummaryPanel(message, bubbleColor);
            contentPanel.add(toolSummaryPanel);

            // 3. 最后显示基于结果的最终回答（如果有）
            if (message.finalContent != null && !message.finalContent.trim().isEmpty()) {
                contentPanel.add(Box.createVerticalStrut(8));
                JTextArea finalArea = new JTextArea(message.finalContent);
                finalArea.setFont(MAIN_FONT);
                finalArea.setForeground(TEXT_PRIMARY);
                finalArea.setBackground(bubbleColor);
                finalArea.setLineWrap(true);
                finalArea.setWrapStyleWord(true);
                finalArea.setEditable(false);
                finalArea.setBorder(null);
                finalArea.setOpaque(false);
                contentPanel.add(finalArea);
            }
        } else {
            // 普通消息，直接显示内容
            if (message.content != null && !message.content.trim().isEmpty()) {
                JTextArea textArea = new JTextArea(message.content);
                textArea.setFont(MAIN_FONT);
                textArea.setForeground(message.isUser ? Color.WHITE : TEXT_PRIMARY);
                textArea.setBackground(bubbleColor);
                textArea.setLineWrap(true);
                textArea.setWrapStyleWord(true);
                textArea.setEditable(false);
                textArea.setBorder(null);
                textArea.setOpaque(false);
                // 关键：不要设置固定的列数或行数，让它根据父容器宽度自适应
                contentPanel.add(textArea);
            }
        }
        bubble.add(contentPanel, BorderLayout.CENTER);

        // 底部面板：时间戳和按钮 (这部分逻辑不变)
        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.setOpaque(false);
        bottomPanel.setBorder(new EmptyBorder(0, 0, 0, 0));
        // 注释时间组件,因为不必增加时间
//        JLabel timeLabel = new JLabel(message.timestamp.format(DateTimeFormatter.ofPattern("HH:mm")));
//        timeLabel.setFont(SMALL_FONT);
//        timeLabel.setForeground(message.isUser ? new Color(255, 255, 255, 180) : TEXT_SECONDARY);
//        bottomPanel.add(timeLabel, BorderLayout.WEST);
        TransparentPanel hoverButtons = createHoverActionButtons(message);
        hoverButtons.setAlpha(0.0f);
        bottomPanel.add(hoverButtons, BorderLayout.EAST);
        bubble.add(bottomPanel, BorderLayout.SOUTH);

        // 添加悬停和右键菜单 (这部分逻辑不变)
        addHoverEffect(bubble, hoverButtons);

        // 3. 创建头像
        JLabel avatarLabel = new JLabel();
        avatarLabel.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 20));
        avatarLabel.setVerticalAlignment(SwingConstants.TOP);

        // 4. 核心布局逻辑：使用 BoxLayout 和 Glue 实现灵活对齐
        if (message.isUser) {
            // 用户消息 (右对齐)
            avatarLabel.setText("👤");
            avatarLabel.setBorder(new EmptyBorder(0, 8, 0, 0)); // 头像在左侧留空隙

            // 先添加弹簧，将内容推向右侧
            container.add(Box.createHorizontalGlue());
            // 添加透明空间
            JPanel spacerJpanel = createSpacerJpanel(avatarLabel.getPreferredSize().width);
            container.add(spacerJpanel);
            // 再添加消息气泡
            container.add(bubble);
            // 最后添加头像
            container.add(avatarLabel);
        } else {
            // 助手消息 (左对齐)
            avatarLabel.setText(getAvatarForRole(message.role));
            avatarLabel.setBorder(new EmptyBorder(0, 0, 0, 8)); // 头像在右侧留空隙

            // 先添加头像
            container.add(avatarLabel);
            // 再添加消息气泡
            container.add(bubble);
            // 添加透明空间
            JPanel spacerJpanel = createSpacerJpanel(avatarLabel.getPreferredSize().width);
            container.add(spacerJpanel);
            // 最后添加弹簧，占据右侧所有剩余空间
            container.add(Box.createHorizontalGlue());
        }

        // 如果启用debug模式，为消息组件添加边框
        if (debugMode) {
            addDebugBorder(container, "MessageContainer", Color.RED);
            addDebugBorder(bubble, "MessageBubble", Color.BLUE);
            addDebugBorder(avatarLabel, "Avatar", Color.GREEN);
            addDebugBorder(contentPanel, "ContentPanel", Color.ORANGE);
        }

        return container;
    }

    private static JPanel createSpacerJpanel(int width) {
        JPanel spacer = new JPanel();

        // 1. 设置首选尺寸
        spacer.setPreferredSize(new Dimension(width, 1)); // 宽度100，高度可以设为任意小值，如1

        // 2. 设置为透明，这样它就不会绘制背景
        spacer.setOpaque(false);

        // 3. 确保它没有边框 (JPanel默认没有，但最好明确)
        spacer.setBorder(null);

        return spacer;
    }

    /**
     * 创建悬停时显示的动作按钮
     */
    private TransparentPanel createHoverActionButtons(Message message) {
        TransparentPanel buttonPanel = new TransparentPanel(new FlowLayout(FlowLayout.RIGHT, 6, 0));
        buttonPanel.setOpaque(false);
        
        // 复制按钮 - 无背景，只有图标
        JButton copyButton = createCleanButton("📋", "Copy message", e -> {
            String textToCopy = message.hasToolCalls ?
                (message.initialContent + "\n\n" + message.finalContent).trim() :
                message.content;

            if (textToCopy != null && !textToCopy.isEmpty()) {
                java.awt.datatransfer.StringSelection stringSelection =
                    new java.awt.datatransfer.StringSelection(textToCopy);
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(stringSelection, null);
            }
        });

        // 编辑按钮 - 无背景，只有图标
        JButton editButton = createCleanButton("✏️", "Edit message", e -> {
            showEditMessageDialog(message);
        });

        // 删除按钮 - 无背景，只有图标
        JButton deleteButton = createCleanButton("🗑", "Delete message", e -> {
            if (deleteMessagesListener != null) {
                List<Integer> indices = new ArrayList<>();
                indices.add(message.index);
                deleteMessagesListener.onDeleteMessages(indices);
            }
        });

        buttonPanel.add(copyButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);
        
        return buttonPanel;
    }
    
    /**
     * 创建简洁的按钮（无背景）
     */
    private JButton createCleanButton(String icon, String tooltip, ActionListener action) {
        JButton button = new JButton(icon);
        button.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 16));
        button.setToolTipText(tooltip);
        button.setBorder(null);
        button.setFocusPainted(false);
        button.setContentAreaFilled(false);
        button.setOpaque(false);
        button.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        button.setPreferredSize(new Dimension(24, 24));
        
        // 悬停时轻微放大效果
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 18));
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                button.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 16));
            }
        });
        
        button.addActionListener(action);
        return button;
    }

    /**
     * 递归地为一个组件及其所有子组件添加一个鼠标监听器。
     * @param c        起始组件 (通常是一个容器)
     * @param listener 要添加的监听器
     */
    public static void addRecursiveMouseListener(Component c, MouseListener listener) {
        // 1. 为当前组件自己添加监听器
        c.addMouseListener(listener);

        // 2. 如果当前组件是一个容器，就遍历它的子组件
        if (c instanceof Container) {
            Container container = (Container) c;
            // 遍历所有子组件
            for (Component child : container.getComponents()) {
                // 对每个子组件，再次调用这个递归函数
                addRecursiveMouseListener(child, listener);
            }
        }
    }

    /**
     * 添加悬停效果到消息气泡
     */
    private void addHoverEffect(JComponent bubble, TransparentPanel hoverButtons) {
        MouseAdapter mouseAdapter = new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                hoverButtons.setAlpha(1.0f);

                bubble.repaint();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                // 检查鼠标是否移动到按钮上
                Point mousePoint = e.getPoint();
                SwingUtilities.convertPointToScreen(mousePoint, bubble);

                Point buttonPoint = hoverButtons.getLocationOnScreen();
                Rectangle buttonBounds = new Rectangle(buttonPoint.x, buttonPoint.y,
                        hoverButtons.getWidth(), hoverButtons.getHeight());

                if (!buttonBounds.contains(mousePoint)) {
                    hoverButtons.setAlpha(0.0f);
                    bubble.repaint();
                }
            }
        };
        addRecursiveMouseListener(bubble, mouseAdapter);

        // 为按钮面板也添加鼠标监听，保持显示状态
        hoverButtons.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseExited(MouseEvent e) {
                // 延迟隐藏，给用户时间点击
                Timer timer = new Timer(200, evt -> {
                    if (!hoverButtons.contains(hoverButtons.getMousePosition())) {
                        hoverButtons.setAlpha(0.0f);
                        bubble.repaint();
                    }
                });
                timer.setRepeats(false);
                timer.start();
            }
        });
    }
    

    
    /**
     * 创建工具调用摘要面板（可展开/折叠）
     */
    private JPanel createToolSummaryPanel(Message message, Color backgroundColor) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(backgroundColor);
        panel.setOpaque(false);
        
        // 摘要行
        JPanel summaryRow = new JPanel(new BorderLayout());
        summaryRow.setBackground(backgroundColor);
        summaryRow.setOpaque(false);
        
        // 左侧：工具图标和摘要文本
        JPanel leftInfo = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        leftInfo.setBackground(backgroundColor);
        leftInfo.setOpaque(false);
        
        JLabel toolIcon = new JLabel("🔧");
        toolIcon.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 14));
        toolIcon.setBorder(new EmptyBorder(0, 0, 0, 6));
        
        String summaryText = String.format("Used %d tool%s", 
            message.toolCalls.size(), 
            message.toolCalls.size() > 1 ? "s" : "");
        JLabel summaryLabel = new JLabel(summaryText);
        summaryLabel.setFont(SMALL_FONT);
        summaryLabel.setForeground(TEXT_SECONDARY);
        
        leftInfo.add(toolIcon);
        leftInfo.add(summaryLabel);
        
        // 右侧：展开/折叠按钮
        JButton toggleButton = new JButton("▼");
        toggleButton.setFont(new Font("Segoe UI Symbol", Font.PLAIN, 10));
        toggleButton.setForeground(TEXT_SECONDARY);
        toggleButton.setBackground(backgroundColor);
        toggleButton.setBorder(new EmptyBorder(2, 6, 2, 6));
        toggleButton.setFocusPainted(false);
        toggleButton.setContentAreaFilled(false);
        toggleButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        
        summaryRow.add(leftInfo, BorderLayout.WEST);
        summaryRow.add(toggleButton, BorderLayout.EAST);
        
        // 详情面板（初始隐藏）
        JPanel detailsPanel = createToolDetailsPanel(message, backgroundColor);
        detailsPanel.setVisible(false);
        
        // 点击事件：展开/折叠
        toggleButton.addActionListener(e -> {
            boolean isExpanded = detailsPanel.isVisible();
            detailsPanel.setVisible(!isExpanded);
            toggleButton.setText(isExpanded ? "▼" : "▲");
            
            // 重新验证布局
            SwingUtilities.getWindowAncestor(panel).revalidate();
            SwingUtilities.getWindowAncestor(panel).repaint();
        });
        
        panel.add(summaryRow, BorderLayout.NORTH);
        panel.add(detailsPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建工具调用详情面板
     */
    private JPanel createToolDetailsPanel(Message message, Color backgroundColor) {
        JPanel detailsPanel = new JPanel();
        detailsPanel.setLayout(new BoxLayout(detailsPanel, BoxLayout.Y_AXIS));
        detailsPanel.setBackground(backgroundColor);
        detailsPanel.setOpaque(false);
        detailsPanel.setBorder(new EmptyBorder(8, 0, 0, 0));
        
        // 显示每个工具调用
        for (int i = 0; i < message.toolCalls.size(); i++) {
            OpenAIRequest.ToolCall toolCall = message.toolCalls.get(i);
            
            // 工具调用信息
            JPanel toolPanel = new RoundedPanel(6, new Color(0, 0, 0, 30));
            toolPanel.setLayout(new BorderLayout());
            toolPanel.setBorder(new EmptyBorder(8, 10, 8, 10));
            
            String toolInfo = String.format("🔧 %s", toolCall.getFunction().getName());
            JLabel toolLabel = new JLabel(toolInfo);
            toolLabel.setFont(SMALL_FONT);
            toolLabel.setForeground(TEXT_PRIMARY);
            
            toolPanel.add(toolLabel, BorderLayout.NORTH);
            
            // 参数信息（如果有）
            String args = toolCall.getFunction().getArguments();
            if (args != null && !args.trim().isEmpty() && !args.equals("{}")) {
                JTextArea argsArea = new JTextArea(formatJsonForDisplay(args));
                argsArea.setFont(new Font("Monaco", Font.PLAIN, 11));
                argsArea.setForeground(TEXT_SECONDARY);
                argsArea.setBackground(new Color(0, 0, 0, 0));
                argsArea.setLineWrap(true);
                argsArea.setWrapStyleWord(true);
                argsArea.setEditable(false);
                argsArea.setBorder(new EmptyBorder(4, 0, 0, 0));
                argsArea.setOpaque(false);
                
                toolPanel.add(argsArea, BorderLayout.CENTER);
            }
            
            // 工具结果（如果有）
            if (i < message.toolResults.size()) {
                OpenAIRequest.Message result = message.toolResults.get(i);
                JTextArea resultArea = new JTextArea("Result: " + 
                    (result.getContent().length() > 100 ? 
                     result.getContent().substring(0, 100) + "..." : 
                     result.getContent()));
                resultArea.setFont(SMALL_FONT);
                resultArea.setForeground(TEXT_SECONDARY);
                resultArea.setBackground(new Color(0, 0, 0, 0));
                resultArea.setLineWrap(true);
                resultArea.setWrapStyleWord(true);
                resultArea.setEditable(false);
                resultArea.setBorder(new EmptyBorder(4, 0, 0, 0));
                resultArea.setOpaque(false);
                
                toolPanel.add(resultArea, BorderLayout.SOUTH);
            }
            
            detailsPanel.add(toolPanel);
            if (i < message.toolCalls.size() - 1) {
                detailsPanel.add(Box.createVerticalStrut(4));
            }
        }
        
        return detailsPanel;
    }
    
    /**
     * 根据角色获取角色图标
     */
    private String getRoleIcon(String role) {
        switch (role) {
            case "tool_call":
                return "🔧";
            case "function":
            case "tool":
                return "📊";
            case "system":
                return "⚙️";
            default:
                return null;
        }
    }
    
    /**
     * 根据角色获取头像
     */
    private String getAvatarForRole(String role) {
        switch (role) {
            case "tool_call":
                return "🔧";
            case "function":
            case "tool":
                return "📊";
            case "system":
                return "⚙️";
            case "assistant":
            default:
                return "🤖";
        }
    }
    
    private void scrollToBottom() {
        SwingUtilities.invokeLater(() -> {
            JScrollBar verticalBar = scrollPane.getVerticalScrollBar();
            verticalBar.setValue(verticalBar.getMaximum());
        });
    }
    
    private void clearChat() {
        if (clearSessionListener != null) {
            // 使用外部回调
            clearSessionListener.onClearSession();
        } else {
            // 降级处理：直接清理UI（保持向后兼容）
            int result = JOptionPane.showConfirmDialog(
                this,
                "Are you sure you want to clear the chat history?",
                "Clear Chat",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE
            );
            
            if (result == JOptionPane.YES_OPTION) {
                messages.clear();
                chatContainer.removeAll();
                chatContainer.revalidate();
                chatContainer.repaint();
            }
        }
    }
    
    // 消息数据类
    private static class Message {
        final String sender;
        final String content;
        final boolean isUser;
        final LocalDateTime timestamp;
        String role;                                    // OpenAI role
        String name;                                    // function name
        List<OpenAIRequest.ToolCall> toolCalls;        // 工具调用列表
        List<OpenAIRequest.Message> toolResults; // 工具结果列表
        boolean hasToolCalls;                           // 是否包含工具调用
        String initialContent;                          // 工具调用前的初始内容
        String finalContent;                            // 基于工具结果的最终回答
        int index;                                      // 消息在列表中的索引
        
        Message(String sender, String content, boolean isUser, LocalDateTime timestamp) {
            this.sender = sender;
            this.content = content;
            this.isUser = isUser;
            this.timestamp = timestamp;
            this.toolCalls = new ArrayList<>();
            this.toolResults = new ArrayList<>();
            this.hasToolCalls = false;
        }
    }
    
    // 圆角面板组件
    private static class RoundedPanel extends JPanel {
        private final int radius;
        private final Color backgroundColor;

        RoundedPanel(int radius, Color backgroundColor) {
            this.radius = radius;
            this.backgroundColor = backgroundColor;
            setOpaque(false);
        }

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2.setColor(backgroundColor);
            g2.fill(new RoundRectangle2D.Float(0, 0, getWidth(), getHeight(), radius, radius));
            g2.dispose();
        }
    }
    
    // ========== ChatInterface 接口实现 ==========

    @Override
    public void setMessages(List<OpenAIRequest.Message> openAIMessages) {
        SwingUtilities.invokeLater(() -> {
            // 清空现有消息
            messages.clear();
            chatContainer.removeAll();
            
            // 智能合并消息，将工具调用序列合并为一个完整的AI回复
            List<Message> mergedMessages = mergeToolCallMessages(openAIMessages);
            
            // 添加合并后的消息并设置索引
            for (int i = 0; i < mergedMessages.size(); i++) {
                Message message = mergedMessages.get(i);
                message.index = i;
                messages.add(message);
                
                JPanel messagePanel = createMessagePanel(message);
                chatContainer.add(messagePanel);
                chatContainer.add(Box.createVerticalStrut(12));
            }
            
            scrollToBottom();
            chatContainer.revalidate();
            chatContainer.repaint();
        });
    }
    
    /**
     * 智能合并工具调用相关的消息
     * 将 assistant(with tool_calls) + tool results + assistant(final answer) 合并为一个消息
     */
    private List<Message> mergeToolCallMessages(List<OpenAIRequest.Message> openAIMessages) {
        List<Message> result = new ArrayList<>();
        
        for (int i = 0; i < openAIMessages.size(); i++) {
            OpenAIRequest.Message current = openAIMessages.get(i);
            
            // 跳过 system 消息为空的情况
            if ("system".equals(current.getRole()) && 
                (current.getContent() == null || current.getContent().trim().isEmpty())) {
                continue;
            }
            
            // 处理用户消息和系统消息
            if ("user".equals(current.getRole()) || "system".equals(current.getRole())) {
                Message message = createBasicMessage(current);
                result.add(message);
                continue;
            }
            
            // 处理 assistant 消息
            if ("assistant".equals(current.getRole())) {
                // 检查是否有工具调用
                if (current.getToolCalls() != null && !current.getToolCalls().isEmpty()) {
                    // 这是一个带工具调用的 assistant 消息，需要合并后续的工具结果和最终回答
                    Message mergedMessage = createToolCallMessage(openAIMessages, i);
                    result.add(mergedMessage);
                    
                    // 跳过已经处理的消息
                    i = findEndOfToolCallSequence(openAIMessages, i);
                } else {
                    // 普通的 assistant 消息
                    Message message = createBasicMessage(current);
                    result.add(message);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 创建基本消息
     */
    private Message createBasicMessage(OpenAIRequest.Message openAIMsg) {
        String senderName = getSenderName(openAIMsg.getRole());
        boolean isUser = "user".equals(openAIMsg.getRole());
        
        Message message = new Message(senderName, openAIMsg.getContent(), isUser, LocalDateTime.now());
        message.role = openAIMsg.getRole();
        message.name = openAIMsg.getName();
        return message;
    }
    
    /**
     * 创建包含工具调用的合并消息
     */
    private Message createToolCallMessage(List<OpenAIRequest.Message> messages, int startIndex) {
        OpenAIRequest.Message initialMsg = messages.get(startIndex);

        // 收集工具调用和结果
        List<OpenAIRequest.ToolCall> toolCalls = new ArrayList<>(initialMsg.getToolCalls());
        List<OpenAIRequest.Message> toolResults = new ArrayList<>();
        String finalAnswer = "";
        
        // 查找相关的工具结果和最终回答
        for (int i = startIndex + 1; i < messages.size(); i++) {
            OpenAIRequest.Message msg = messages.get(i);
            
            if ("tool".equals(msg.getRole()) || "function".equals(msg.getRole())) {
                toolResults.add(msg);
            } else if ("assistant".equals(msg.getRole()) && 
                      (msg.getToolCalls() == null || msg.getToolCalls().isEmpty()) &&
                      msg.getContent() != null && !msg.getContent().trim().isEmpty()) {
                finalAnswer = msg.getContent();
                break;
            } else if ("user".equals(msg.getRole())) {
                // 遇到新的用户消息，停止合并
                break;
            }
        }
        
        // 创建合并消息
        String initialContent = initialMsg.getContent();
        if (initialContent == null || initialContent.trim().isEmpty()) {
            initialContent = ""; // 如果没有初始内容，则为空
        }
        
        // 使用完整的内容作为主要显示内容（用于搜索等功能）
        String fullContent = initialContent;
        if (!finalAnswer.isEmpty()) {
            if (!fullContent.isEmpty()) {
                fullContent += "\n\n" + finalAnswer;
            } else {
                fullContent = finalAnswer;
            }
        }
        
        Message message = new Message("GitHub Copilot", fullContent, false, LocalDateTime.now());
        message.role = "assistant";
        message.hasToolCalls = true;
        message.toolCalls = toolCalls;
        message.toolResults = toolResults;
        message.initialContent = initialContent;
        message.finalContent = finalAnswer;
        
        return message;
    }
    
    /**
     * 找到工具调用序列的结束位置
     */
    private int findEndOfToolCallSequence(List<OpenAIRequest.Message> messages, int startIndex) {
        for (int i = startIndex + 1; i < messages.size(); i++) {
            OpenAIRequest.Message msg = messages.get(i);
            
            if ("user".equals(msg.getRole())) {
                return i - 1;
            }
            
            if ("assistant".equals(msg.getRole()) && 
                (msg.getToolCalls() == null || msg.getToolCalls().isEmpty()) &&
                msg.getContent() != null && !msg.getContent().trim().isEmpty()) {
                return i;
            }
        }
        return messages.size() - 1;
    }
    
    @Override
    public void onMessageSent(MessageSentListener listener) {
        this.messageSentListener = listener;
    }
    
    @Override
    public void setReplyingStatus(boolean isReplying) {
        SwingUtilities.invokeLater(() -> {
            if (isReplying) {
                startTyping();
            } else {
                stopTyping();
            }
        });
    }
    
    @Override
    public void onClearSession(ClearSessionListener listener) {
        this.clearSessionListener = listener;
    }
    
    @Override
    public void onDeleteMessages(DeleteMessagesListener listener) {
        this.deleteMessagesListener = listener;
    }

    private void handleEditMessages(int i, String s) {

    }

    @Override
    public void onMessageEdited(MessageEditedListener listener) {
        this.messageEditedListener = listener;
    }

    /**
     * 显示消息编辑对话框
     */
    private void showEditMessageDialog(Message message) {
        // 获取要编辑的文本内容
        String currentContent = message.hasToolCalls ?
            (message.initialContent != null ? message.initialContent : "") :
            (message.content != null ? message.content : "");

        // 创建编辑对话框
        JDialog editDialog = new JDialog(this, "编辑消息", true);
        editDialog.setSize(500, 300);
        editDialog.setLocationRelativeTo(this);
        editDialog.setLayout(new BorderLayout());

        // 创建文本编辑区域
        JTextArea textArea = new JTextArea(currentContent);
        textArea.setFont(MAIN_FONT);
        textArea.setLineWrap(true);
        textArea.setWrapStyleWord(true);
        textArea.setBackground(BACKGROUND);
        textArea.setForeground(TEXT_PRIMARY);
        textArea.setBorder(new EmptyBorder(10, 10, 10, 10));

        // 添加滚动面板
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setBorder(null);
        scrollPane.setBackground(BACKGROUND);
        editDialog.add(scrollPane, BorderLayout.CENTER);

        // 创建按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setBackground(BACKGROUND);
        buttonPanel.setBorder(new EmptyBorder(10, 10, 10, 10));

        // 取消按钮
        JButton cancelButton = new JButton("取消");
        cancelButton.setFont(MAIN_FONT);
        cancelButton.addActionListener(e -> editDialog.dispose());

        // 确认按钮
        JButton confirmButton = new JButton("确认");
        confirmButton.setFont(MAIN_FONT);
        confirmButton.addActionListener(e -> {
            String newContent = textArea.getText().trim();
            if (!newContent.equals(currentContent)) {
                // 触发编辑回调
                if (messageEditedListener != null) {
                    messageEditedListener.onMessageEdited(message.index, newContent);
                }
            }
            editDialog.dispose();
        });

        // 保存并重试按钮
        JButton saveAndRetryButton = new JButton("保存并重试");
        saveAndRetryButton.setFont(MAIN_FONT);
        saveAndRetryButton.addActionListener(e -> {
            String newContent = textArea.getText().trim();

            // 先触发编辑事件逻辑
            if (!newContent.equals(currentContent)) {
                if (messageEditedListener != null) {
                    messageEditedListener.onMessageEdited(message.index, newContent);
                }
            }

            // 关闭对话框
            editDialog.dispose();

            // 再触发消息发送事件
            if (messageSentListener != null) {
                messageSentListener.onMessageSent(null);
            }
        });

        buttonPanel.add(cancelButton);
        buttonPanel.add(confirmButton);
        buttonPanel.add(saveAndRetryButton);
        editDialog.add(buttonPanel, BorderLayout.SOUTH);

        // 设置默认按钮和键盘快捷键
        editDialog.getRootPane().setDefaultButton(confirmButton);

        // ESC键关闭对话框
        editDialog.getRootPane().registerKeyboardAction(
            e -> editDialog.dispose(),
            KeyStroke.getKeyStroke(KeyEvent.VK_ESCAPE, 0),
            JComponent.WHEN_IN_FOCUSED_WINDOW
        );

        // 显示对话框
        textArea.requestFocus();
        textArea.selectAll();
        editDialog.setVisible(true);
    }

    /**
     * 根据OpenAI角色获取显示名称
     */
    private String getSenderName(String role) {
        switch (role) {
            case "user":
                return "You";
            case "assistant":
                return "GitHub Copilot";
            case "system":
                return "System";
            case "function":
                return "Function Result";
            case "tool":
                return "Tool Result";
            default:
                return role;
        }
    }
    
    public static void main(String[] args) {
        // 检查是否启用debug模式
        boolean debugMode = false;
        for (String arg : args) {
            if ("--debug".equals(arg) || "-d".equals(arg)) {
                debugMode = true;
                System.out.println("Debug mode enabled - UI component borders will be visible");
                break;
            }
        }

        // 设置系统外观
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 设置抗锯齿
        System.setProperty("awt.useSystemAAFontSettings", "on");
        System.setProperty("swing.aatext", "true");

        final boolean finalDebugMode = debugMode;
        SwingUtilities.invokeLater(() -> {
            new CopilotChatAssistant(finalDebugMode).setVisible(true);
        });
    }
}